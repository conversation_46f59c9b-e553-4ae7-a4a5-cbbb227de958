import {
  BadRequestException,
  HttpException,
  HttpStatus,
  Injectable,
  Logger,
  NotFoundException,
} from '@nestjs/common';
import { PrismaService } from '../common/prisma/prisma.service';
import { EncounterDetailsDTO, CreateEncounterDTO } from './dtos';
import { PaginatedResultDTO, PaginationResDTO } from '../common/dtos';
import { Encounter } from '@prisma/client';
import { EncounterSummeryDto } from './dtos/EncounterDetails.dto';
import { STATUS } from 'src/common/constants/common.constants';
import { UtilsService } from '../common/utils/utils.service';

@Injectable()
export class EncounterRepository {
  private readonly logger = new Logger(EncounterRepository.name);
  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async insertEncounter(
    data: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    doctor,
  ): Promise<EncounterDetailsDTO> {
    try {
      const encounter = await this.prisma.encounter.create({
        data: {
          encounter_date: new Date(),
          doctor_id: doctor.doctor_id, // Associate with doctor
          note_type_id: data.note_type_id || null, // Associate with note type
          encounter_status: 'NOT_STARTED',
          patient_id: data.patient_id,
        },
        select: {
          encounter_id: true,
          encounter_date: true,
          patient: true,
          encounter_status: true,
        },
      });

      return {
        encounterId: encounter.encounter_id,
        encounterDate: encounter.encounter_date,
        encounterStatus: encounter.encounter_status,
        patient: {
          patientId: encounter.patient.patient_id,
          patientFirstName: encounter.patient.first_name,
          patientLastName: encounter.patient.last_name,
          patientDOB: encounter.patient.date_of_birth
            .toISOString()
            .split('T')[0],
          patientGender: encounter.patient.gender,
          patientPhone: encounter.patient.phone_number,
          phoneCountryCode: encounter.patient.phone_country_code,
        },
        doctor: {
          doctorId: doctor.doctor_id,
          email: doctor.email,
        },
      };
    } catch (error) {
      this.logger.error(`Error creating encounter: error:${error?.stack}`);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create new encounter',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getEncounterById(encounterId: string): Promise<EncounterDetailsDTO> {
    try {
      const encounter = await this.prisma.encounter.findUnique({
        where: {
          encounter_id: encounterId,
          isActive: true,
        },
        select: {
          encounter_id: true,
          encounter_date: true,
          encounter_status: true,
          note_type_id: true,
          patient: {
            select: {
              patient_id: true,
              first_name: true,
              last_name: true,
              gender: true,
              date_of_birth: true,
              phone_number: true,
              phone_country_code: true,
            },
          },
          doctor: true,
        },
      });

      if (!encounter) {
        this.logger.error(
          `Encounter not found for given encounter id :${encounterId}`,
        );
        throw new NotFoundException(
          `Encounter not found for given encounter id :${encounterId}`,
        );
      }

      return {
        encounterId: encounter.encounter_id,
        encounterDate: encounter.encounter_date,
        encounterStatus: encounter.encounter_status,
        noteTypeId: encounter.note_type_id,
        patient: {
          patientId: encounter.patient.patient_id,
          patientFirstName: encounter.patient.first_name,
          patientLastName: encounter.patient.last_name,
          patientDOB: encounter.patient.date_of_birth.toISOString(),
          patientGender: encounter.patient.gender,
          patientPhone: encounter.patient.phone_number,
          phoneCountryCode: encounter.patient.phone_country_code,
        },
        doctor: {
          doctorId: encounter.doctor.doctor_id,
          email: encounter.doctor.email,
        },
      };
    } catch (error) {
      this.logger.error(
        `Error retrieving encounter:encounter_id:${encounterId} error:${error?.stack}`,
      );
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to retrieve encounter information',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllEncountersByDoctorEmail(
    doctorEmail: string,
    page: number,
    limit: number,
    patient_name: string | undefined = undefined,
    patient_id: string | undefined = undefined,
  ): Promise<PaginatedResultDTO<EncounterDetailsDTO>> {
    try {
      const [encounters, totalRecords] = await Promise.all([
        this.prisma.encounter.findMany({
          where: {
            doctor: {
              email: doctorEmail,
            },
            isActive: true,
            patient: patient_name
              ? {
                  OR: [
                    {
                      isActive: true,
                      first_name: {
                        startsWith: patient_name,
                        mode: 'insensitive',
                      },
                    },
                    {
                      isActive: true,
                      last_name: {
                        startsWith: patient_name,
                        mode: 'insensitive',
                      },
                    },
                  ],
                }
              : undefined,

            patient_id: patient_id ? patient_id : undefined,
          },
          include: {
            patient: {
              select: {
                patient_id: true,
                first_name: true,
                last_name: true,
                gender: true,
                date_of_birth: true,
                phone_number: true,
                phone_country_code: true,
              },
            },
            doctor: {
              select: {
                doctor_id: true,
                email: true,
              },
            },

            encounterstatus: {
              select: {
                label: true,
              },
            },
            audio_transcriptions: {
              select: {
                duration: true,
              },
            },
          },
          skip: (page - 1) * limit,
          take: limit,
          orderBy: { updated_at: 'desc' },
        }),
        this.prisma.encounter.count({
          where: {
            doctor: { email: doctorEmail },
            isActive: true,
            patient: patient_name
              ? {
                  OR: [
                    {
                      isActive: true,
                      first_name: {
                        startsWith: patient_name,
                        mode: 'insensitive',
                      },
                    },
                    {
                      isActive: true,
                      last_name: {
                        startsWith: patient_name,
                        mode: 'insensitive',
                      },
                    },
                  ],
                }
              : undefined,

            patient_id: patient_id ? patient_id : undefined,
          },
        }),
      ]);

      if (!encounters || encounters.length === 0) {
        return { data: [], pagination: {} };
      }

      const totalPages = Math.ceil(totalRecords / limit);

      const pagination: PaginationResDTO = {
        currentPage: page,
        recordsPerPage: limit,
        totalRecords,
        totalPages,
      };

      const mappedEncounters = encounters.map((encounter) => ({
        encounterId: encounter.encounter_id,
        encounterDate: encounter.encounter_date,
        encounterStatus: encounter.encounter_status,
        updated_at: encounter.updated_at,
        patient: {
          patientId: encounter.patient.patient_id,
          patientFirstName: encounter.patient.first_name,
          patientLastName: encounter.patient.last_name,
          patientDOB: encounter.patient.date_of_birth
            .toISOString()
            .split('T')[0],
          patientGender: encounter.patient.gender,
          patientPhone: encounter.patient.phone_number,
          phoneCountryCode: encounter.patient.phone_country_code,
        },
        doctor: {
          doctorId: encounter.doctor.doctor_id,
          email: encounter.doctor.email,
        },
        encounterstatus: {
          label: encounter.encounterstatus.label,
        },
        audioTranscription: encounter.audio_transcriptions,
      }));

      return {
        data: mappedEncounters,
        pagination,
      };
    } catch (error) {
      this.logger.error(
        `Error fetching encounters for doctor: doctoremail:${doctorEmail}`,
      );
      if (error instanceof NotFoundException) {
        throw this.utilsService.formatErrorResponse(
          error,
          error.message,
          HttpStatus.NOT_FOUND,
        );
      }
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to retrieve encounters list',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateEncounterStatus(
    encounterId: string,
    status: string,
  ): Promise<Encounter> {
    try {
      const statusExists = await this.prisma.encounterStatus.findFirst({
        where: {
          status: status,
          isActive: true,
        },
      });

      if (!statusExists) {
        throw new BadRequestException('Invalid encounter status');
      }

      const encounterExistsCount = await this.prisma.encounter.count({
        where: {
          isActive: true,
          encounter_id: encounterId,
        },
      });

      if (encounterExistsCount === 0) {
        this.logger.error(
          `encounter  update failed encounterid:${encounterId} status:${status}`,
        );
        throw new HttpException(
          `encounter not found for update status encounterid:${encounterId} status:${status}`,
          HttpStatus.NOT_FOUND,
        );
      }

      const encounter = await this.prisma.encounter.update({
        where: {
          encounter_id: encounterId,
          isActive: true,
        },
        data: {
          encounter_status: status,
        },
      });

      return encounter;
    } catch (error) {
      this.logger.error(
        `Error updating encounter status encounterid:${encounterId} status:${status} error:${error?.stack}`,
      );
      if (error instanceof BadRequestException) {
        throw this.utilsService.formatErrorResponse(
          error,
          error.message,
          HttpStatus.BAD_REQUEST,
        );
      }
      if (error instanceof NotFoundException) {
        throw this.utilsService.formatErrorResponse(
          error,
          error.message,
          HttpStatus.NOT_FOUND,
        );
      }
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update encounter status',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
      
    }
  }

  async singOffEncounter(
    encounterId: string,
    userEmail: string,
  ): Promise<boolean> {
    try {
      let transactionarray = [];

      //for labs
      transactionarray.push(
        this.prisma.labs.updateMany({
          where: {
            isActive: true,
            encounter_id: encounterId,
            status: 'PENDING',
            created_by: 'Transcript',
          },
          data: {
            status: 'ACCEPTED',
            updated_by: userEmail,
          },
        }),
      );
      transactionarray.push(
        this.prisma.labs.updateMany({
          where: {
            isActive: true,
            encounter_id: encounterId,
            status: 'PENDING',
            created_by: 'Suggested',
          },
          data: {
            status: 'REJECTED',
            updated_by: userEmail,
          },
        }),
      );

      //for prescriptions
      transactionarray.push(
        this.prisma.prescriptions.updateMany({
          where: {
            isActive: true,
            encounter_id: encounterId,
            status: 'PENDING',
            created_by: 'Transcript',
          },
          data: {
            status: 'ACCEPTED',
            updated_by: userEmail,
          },
        }),
      );

      await this.prisma.$transaction([...transactionarray]);

      return true;
    } catch (error) {
      this.logger.error(
        `Error appling singoff encounter: encounterid:${encounterId} error:${error?.stack}`,
      );
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to apply singoff encounters',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async deleteEncounter(encounter_id: string, userEmail: string) {
    try {
      const existingEncounter = await this.prisma.encounter.findFirst({
        where: {
          encounter_id: encounter_id,
          isActive: true,
        },
        select: {
          encounter_id: true,
        },
      });

      if (!existingEncounter) {
        this.logger.error(
          `Encounter not found for given encounter id:${encounter_id}`,
        );
        throw new NotFoundException(
          `Encounter not found for given encounter id:${encounter_id}`,
        );
      }

      const encounter = await this.prisma.encounter.update({
        where: {
          encounter_id: encounter_id,
          isActive: true,
        },
        data: {
          isActive: false,
          updated_at: new Date(),
          updated_by: userEmail,
        },
      });
      return encounter;
    } catch (error) {
      this.logger.error(
        `Error deleting encounter: encounter id:${encounter_id} error:${error?.stack}`,
      );
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to delete encounter',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async genEncounterSummary(encounter_id: string, model: EncounterSummeryDto) {
    try {
      const encounter = await this.prisma.encounter.findUnique({
        where: {
          encounter_id: encounter_id,
          isActive: true,
          encounter_status: 'COMPLETED',
        },
        include: {
          patient: true,
          doctor: true,
          NoteHeader: model.Notes
            ? {
                where: { isActive: true },
                select: {
                  note_type: {
                    select: {
                      note_type_id: true,
                      note_type_name: true,
                    },
                  },
                  note_details: {
                    where: { isActive: true },
                    select: {
                      note_section: {
                        select: {
                          note_section_id: true,
                          section_name: true,
                        },
                      },
                      value: true,
                    },
                  },
                },
              }
            : undefined,
          labs: model.Labs
            ? {
                where: {
                  isActive: true,
                  status: STATUS.ACCEPTED,
                },
              }
            : undefined,
          Prescriptions: model.Prescriptions
            ? {
                where: {
                  isActive: true,
                  status: STATUS.ACCEPTED,
                },
              }
            : undefined,
        },
      });

      if (!encounter) {
        this.logger.error(
          `Encounter not found for the given encounter id :${encounter_id}`,
        );
        throw new HttpException('Encounter not found', HttpStatus.NOT_FOUND);
      }

      return encounter;
    } catch (error) {
      this.logger.error(
        `Error retrieving encounter for the given encounter id :${encounter_id} error:${error?.stack}`,
      );
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to retrieve encounter',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
