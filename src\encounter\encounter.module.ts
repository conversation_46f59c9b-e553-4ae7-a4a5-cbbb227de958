import { <PERSON>du<PERSON> } from '@nestjs/common';
import { EncounterController } from './encounter.controller';
import { EncounterService } from './encounter.service';
import { EncounterRepository } from './encounter.repository';
import { DoctorRepository } from '../doctor/doctor.repository';
import { PatientModule } from 'src/patient/patient.module';
import { PatientRepository } from 'src/patient/patient.repository';

@Module({
  imports: [],
  controllers: [EncounterController],
  providers: [
    EncounterService,
    EncounterRepository,
    DoctorRepository,
    PatientRepository,
  ],
  exports: [EncounterRepository,EncounterService],
})
export class EncounterModule {}
