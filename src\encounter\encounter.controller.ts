import {
  Body,
  Controller,
  Delete,
  Get,
  Optional,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Query,
  Res,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import { CreateEncounterDTO, EncounterDetailsDTO } from './dtos';
import { EncounterService } from './encounter.service';
import {
  ApiResponseDTO,
  PaginatedResultDTO,
  PaginationQueryDTO,
} from '../common/dtos';
import { GetUserEmail } from '../common/decorators/get-user-email.decorator';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { Encounter } from '@prisma/client';
import { EncounterSummeryDto } from './dtos/EncounterDetails.dto';

@ApiTags('Encounters')
@ApiBearerAuth('access-token')
@Controller('encounters')
export class EncounterController {
  constructor(private readonly encounterService: EncounterService) {}

  @Post()
  @UsePipes(new ValidationPipe())
  @ApiOperation({ summary: 'Create a new medical encounter' })
  async createEncounter(
    @Body() data: CreateEncounterDTO,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<EncounterDetailsDTO>> {
    const encounter = await this.encounterService.insertEncounter(
      data,
      userEmail,
    );
    return {
      data: encounter,
    };
  }

  @Get(':encounterId')
  @ApiOperation({ summary: 'Get encounter for encounterId' })
  async getEncounterById(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
  ): Promise<ApiResponseDTO<EncounterDetailsDTO>> {
    const encounter = await this.encounterService.getEncounterById(encounterId);
    return {
      statusCode: 200,
      message: 'Encounter Details fetched successfully',
      data: encounter,
    };
  }

  @Get()
  @ApiOperation({
    summary: 'Retrieve all encounters for the authenticated doctor',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'patient_name',
    required: false,
    type: String,
    description: 'Filter encounters by patient name',
  })
  @ApiQuery({
    name: 'patient_id',
    required: false,
    type: String,
    description: 'Filter encounters by patient id',
  })
  async getAllEncounters(
    @Query() pagination: PaginationQueryDTO,
    @GetUserEmail() userEmail: string,
    @Query('patient_name') patient_name: string,
    @Query('patient_id') patient_id: string,
  ): Promise<PaginatedResultDTO<EncounterDetailsDTO>> {
    const { page, limit } = pagination;

    if (!patient_name || patient_name == '') {
      patient_name = undefined;
    }
    if (!patient_id || patient_id == '') {
      patient_id = undefined;
    }

    return await this.encounterService.getAllEncountersByDoctorEmail(
      userEmail,
      page,
      limit,
      patient_name,
      patient_id,
    );
  }

  @Patch(':encounterId/update-status/:status')
  @ApiOperation({ summary: 'Update encounter status' })
  async updateEncounterStatus(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @Param('status') status: string,
  ): Promise<ApiResponseDTO<Encounter>> {
    const encounter = await this.encounterService.updateEncounterStatus(
      encounterId,
      status,
    );
    return {
      data: encounter,
    };
  }

  @Patch('/sign-off/:encounterId')
  async singOffEncounter(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.encounterService.signOffEncounter(encounterId, userEmail);
  }
  @Delete('/:encounterId')
  async deleteEncounter(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @GetUserEmail() userEmail: string,
  ) {
    return await this.encounterService.deleteEncounter(encounterId, userEmail);
  }

  @Post('/summary/:encounterId')
  async getEncounterSummary(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
    @Body() model: EncounterSummeryDto,
  ) {
    return await this.encounterService.getEncounterSummary(encounterId, model);
  }
}
