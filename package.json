{"name": "ekko-coreservice-nodejs", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"build": "nest build", "format": "prettier --write \"src/**/*.ts\" \"test/**/*.ts\"", "start": "nest start", "start:dev": "nest start --watch", "start:debug": "nest start --debug --watch", "start:prod": "node dist/main", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "test:integration": "jest --config jest.integration.config.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.709.0", "@aws-sdk/client-transcribe-streaming": "^3.782.0", "@aws-sdk/lib-storage": "^3.709.0", "@aws-sdk/node-http-handler": "^3.374.0", "@google-cloud/speech": "^7.0.1", "@google-cloud/storage": "^7.16.0", "@nestjs/axios": "^3.1.3", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.3.0", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/passport": "^10.0.3", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^10.0.0", "@nestjs/swagger": "^8.1.0", "@nestjs/terminus": "^10.2.3", "@nestjs/websockets": "^10.0.0", "@prisma/client": "^6.4.1", "@smithy/node-http-handler": "^3.3.2", "@types/uuid": "^10.0.0", "aws-sdk": "^2.1692.0", "axios": "^1.7.9", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "dotenv": "^16.4.7", "fluent-ffmpeg": "^2.1.3", "jwks-rsa": "^3.1.0", "microsoft-cognitiveservices-speech-sdk": "^1.41.0", "multer": "^1.4.5-lts.1", "passport": "^0.7.0", "passport-jwt": "^4.0.1", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "socket.io": "^4.7.2", "swagger-ui-express": "^5.0.1", "uuid": "^11.1.0"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/express": "^5.0.0", "@types/fluent-ffmpeg": "^2.1.27", "@types/jest": "^29.5.2", "@types/multer": "^1.4.12", "@types/node": "^20.3.1", "@types/passport-jwt": "^4.0.1", "@types/supertest": "^6.0.0", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^8.0.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "jest": "^29.5.0", "prettier": "^3.0.0", "prisma": "^6.4.1", "source-map-support": "^0.5.21", "supertest": "^7.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}}