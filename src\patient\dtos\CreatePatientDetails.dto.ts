import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Gender } from '@prisma/client';
import {
  IsAlpha,
  IsDateString,
  IsEnum,
  IsNotEmpty,
  IsPhoneNumber,
  IsString,
  Matches,
  MaxLength,
} from 'class-validator';

export class CreatePatientDetailsDto {
  @ApiProperty({
    description: 'First name of the patient',
    example: 'Alice',
  })
  @IsNotEmpty({ message: 'Patient first name is required' })
  @Matches(/^[A-Za-z]+(?:\s+[A-Za-z]+)*$/, {
    message:
      'Patient first name must contain letters and may include spaces between words',
  })
  patientFirstName: string;

  @ApiProperty({
    description: 'Last name of the patient',
    example: '<PERSON>',
  })
  @IsNotEmpty({ message: 'Patient last name is required' })
  @Matches(/^[A-Za-z]+(?:\s+[A-Za-z]+)*$/, {
    message:
      'Patient last name must contain letters and may include spaces between words',
  })
  patientLastName: string;

  @ApiProperty({
    description: 'Gender of the patient',
    enum: Gender,
    example: Gender.FEMALE,
  })
  @IsNotEmpty({ message: 'Patient gender is required' })
  @IsEnum(Gender, {
    message: `Patient gender must be one of: ${Object.values(Gender).join(', ')}`,
  })
  patientGender: Gender;

  @ApiProperty({
    description: 'Date of birth of the patient in ISO 8601 format',
    example: '1990-01-01',
    type: String,
    format: 'date',
  })
  @IsNotEmpty({ message: 'Patient date of birth is required' })
  @IsDateString(
    {},
    { message: 'Patient date of birth must be a valid ISO 8601 date string' },
  )
  patientDOB: string;

  @ApiPropertyOptional({
    description: "Patient's phone number in E.164 format",
    example: '+919876543211',
  })
  @IsPhoneNumber('IN', {
    message: 'Patient phone must be a valid phone number',
  })
  @MaxLength(20, {
    message: 'Patient phone number must not exceed 20 characters',
  })
  patientPhone?: string;

  @ApiPropertyOptional({
    description: "Country code of the patient's phone number",
    example: '+91',
    maxLength: 5,
  })
  @IsString({ message: 'Phone country code must be a string' })
  @MaxLength(5, { message: 'Phone country code must not exceed 5 characters' })
  phoneCountryCode?: string;
}
