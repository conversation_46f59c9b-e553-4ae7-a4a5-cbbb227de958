import { Injectable, NotFoundException, Logger } from '@nestjs/common';
import { DoctorLetterHead, letterhead_section, PageSize } from '@prisma/client';
import { PrismaService } from 'src/common/prisma/prisma.service';
import {
  UpdateLetterHeadDto,
  UpsertDoctorLetterheadSectionDto,
  upsertMultipleDoctorLetterheadSectionsDto,
} from './dto/letterhead.dto';
import { CUSTOM_LETTERHEAD_SECTIONS } from 'src/common/constants/common.constants';

@Injectable()
export class LetterHeadRepository {
  private readonly logger = new Logger(LetterHeadRepository.name);

  constructor(private readonly prismaservice: PrismaService) {}

  async getOrCreateLetterHead(userEmail: string) {
    try {
      let letterhead = await this.prismaservice.doctorLetterHead.findMany({
        where: {
          created_by: userEmail,
          isActive: true,
        },
        include: {
          doctor_letterhead_section: {
            where: { isActive: true },
            select: {
              value: true,
              letterhead_section: {
                where: { isActive: true },
                select: {
                  label: true,
                  letterhead_section_id: true,
                },
              },
            },
          },
        },
      });
      if (letterhead && letterhead.length > 0) {
        return letterhead;
      }

      const existingDoctor = await this.prismaservice.doctor.findFirst({
        where: {
          email: userEmail,
          isActive: true,
        },
        select: {
          first_name: true,
          last_name: true,
          phone: true,
          email: true,
        },
      });

      if (!existingDoctor) {
        this.logger.error(`Doctor not found for email: ${userEmail}`);
      }

      if (!letterhead || letterhead.length == 0) {
        const standardlh = this.prismaservice.doctorLetterHead.create({
          data: {
            page_size: 'A4',
            page_height: 11.69,
            page_width: 8.27,
            top_padding: 1.25,
            top_padding_px: 120,
            bottom_padding: 0.5,
            bottom_padding_px: 48,
            units: 'inches',
            is_default: false,
            created_by: userEmail,
            type: 'STANDARD',
            doctor: {
              connect: {
                email: userEmail,
              },
            },
          },
        });
        const customlh = this.prismaservice.doctorLetterHead.create({
          data: {
            page_size: 'A4',
            page_height: 11.69,
            page_width: 8.27,
            top_padding: 1.25,
            top_padding_px: 120,
            bottom_padding: 0.5,
            bottom_padding_px: 48,
            units: 'inches',
            is_default: true,
            created_by: userEmail,
            type: 'CUSTOM',
            doctor: {
              connect: {
                email: userEmail,
              },
            },
            doctor_letterhead_section: {
              create: [
                {
                  letterhead_section_id: CUSTOM_LETTERHEAD_SECTIONS.DOCTOR_NAME,
                  value:
                    (existingDoctor?.first_name ?? '') +
                    ' ' +
                    (existingDoctor?.last_name ?? ''),
                  created_by: userEmail,
                },
                {
                  letterhead_section_id:
                    CUSTOM_LETTERHEAD_SECTIONS.DOCTOR_EMAIL,
                  value: existingDoctor?.email ?? '',
                  created_by: userEmail,
                },
                {
                  letterhead_section_id:
                    CUSTOM_LETTERHEAD_SECTIONS.DOCTOR_PHONE,
                  value: existingDoctor?.phone ?? null,
                  created_by: userEmail,
                },
              ],
            },
          },
          include: {
            doctor_letterhead_section: {
              where: { isActive: true },
              select: {
                value: true,
                letterhead_section: {
                  where: { isActive: true },
                  select: {
                    label: true,
                    letterhead_section_id: true,
                  },
                },
              },
            },
          },
        });
        const insertedletterhead = await this.prismaservice.$transaction([
          standardlh,
          customlh,
        ]);
        return insertedletterhead;
      }
    } catch (error) {
      this.logger.error(`Error in getOrCreateLetterHead for user: ${userEmail}`, error?.stack);
      throw error;
    }
  }

  async updateLetterHead(
    userEmail: string,
    model: UpdateLetterHeadDto,
    letterhead_id: string,
  ): Promise<DoctorLetterHead> {
    try {
      const letterhead = await this.prismaservice.doctorLetterHead.update({
        where: {
          letterhead_id: letterhead_id,
          created_by: userEmail,
          isActive: true,
          doctor: {
            email: userEmail,
          },
        },
        data: {
          page_size: model.page_size,
          page_height: model.page_height,
          page_width: model.page_width,
          top_padding: model.top_padding,
          top_padding_px: model.top_padding_px,
          bottom_padding: model.bottom_padding,
          bottom_padding_px: model.bottom_padding_px,
          units: model.units,
          updated_at: new Date(),
          updated_by: userEmail,
        },
      });
      return letterhead;
    } catch (error) {
      this.logger.error(`Error updating letterhead ID: ${letterhead_id} for user: ${userEmail}`, error?.stack);
      throw error;
    }
  }

  async getAvailablePagesSizes(userEmail: string): Promise<PageSize[]> {
    try {
      const PageSize = await this.prismaservice.pageSize.findMany({
        where: {
          isActive: true,
        },
      });
      return PageSize;
    } catch (error) {
      this.logger.error(`Error fetching available page sizes for user: ${userEmail}`, error?.stack);
      throw error;
    }
  }

  async getDoctorLetterheadSectionWithValues(
    userEmail: string,
  ): Promise<DoctorLetterHead> {
    try {
      const res = await this.prismaservice.doctorLetterHead.findFirst({
        where: {
          isActive: true,
          doctor: {
            email: userEmail,
          },
        },
        include: {
          doctor_letterhead_section: {
            select: {
              value: true,
              letterhead_section: {
                select: {
                  label: true,
                  letterhead_section_id: true,
                },
              },
            },
          },
        },
      });
      return res;
    } catch (error) {
      console.error(
        `Error in getDoctorLetterheadSectionWithValues ${error}`,
        error?.stack,
      );
      throw error;
    }
  }

  async getAllLetterheadSections() {
    try {
      const letterheadsections =
        await this.prismaservice.letterhead_section.findMany({
          where: {
            isActive: true,
          },
          select: {
            letterhead_section_id: true,
            label: true,
            type: true,
            size_constraint: true,
          },
        });
      return letterheadsections;
    } catch (error) {
      console.error(
        `Error in getting all letterhead sections ${error}`,
        error?.stack,
      );
      throw error;
    }
  }

  async upsertDoctorLetterHeadSections(
    userEmail: string,
    model: UpsertDoctorLetterheadSectionDto,
  ) {
    try {
      let insertRes: any;
      const updateRes =
        await this.prismaservice.doctor_letterhead_section.updateMany({
          where: {
            isActive: true,
            letterhead_id: model.letterhead_id,
            letterhead_section_id: model.letterhead_section_id,
          },
          data: {
            value: model.value,
            updated_by: userEmail,
          },
        });

      if (updateRes.count == 0) {
        insertRes =
          await this.prismaservice.doctor_letterhead_section.createMany({
            data: {
              letterhead_id: model.letterhead_id,
              letterhead_section_id: model.letterhead_section_id,
              value: model.value,
              created_by: userEmail,
            },
          });

        return insertRes;
      } else {
        return updateRes;
      }
    } catch (error) {
      console.error(
        `Error in getting all letterhead sections ${error}`,
        error?.stack,
      );
      throw error;
    }
  }

  async toggleDefaultLetterHead(userEmail: string) {
    try {
      const exitingLetterhead =
        await this.prismaservice.doctorLetterHead.findMany({
          where: {
            created_by: userEmail,
            isActive: true,
          },
          select: {
            letterhead_id: true,
            is_default: true,
          },
        });

      let transactionarray = [];

      exitingLetterhead.map((lh) => {
        transactionarray.push(
          this.prismaservice.doctorLetterHead.update({
            where: {
              letterhead_id: lh.letterhead_id,
              isActive: true,
            },
            data: {
              is_default: !lh.is_default,
            },
            select: {
              letterhead_id: true,
              is_default: true,
            },
          }),
        );
      });

      return await this.prismaservice.$transaction(transactionarray);
    } catch (error) {
      console.error(`Error toggling letterhead  ${error}`, error?.stack);
      throw error;
    }
  }

  async upsertMultipleDoctorLetterheadSections(
    userEmail: string,
    model: upsertMultipleDoctorLetterheadSectionsDto,
  ) {
    try {
      const exisitngMapping =
        await this.prismaservice.doctor_letterhead_section.findMany({
          where: {
            OR: model.info.map((item) => ({
              letterhead_id: item.letterhead_id,
              letterhead_section_id: item.letterhead_section_id,
              isActive: true,
            })),
          },
          select: {
            letterhead_id: true,
            letterhead_section_id: true,
          },
        });

      let transactionarray = [];

      model.info.map((item) => {
        let index = exisitngMapping.findIndex(
          (x) =>
            x.letterhead_id == item.letterhead_id &&
            x.letterhead_section_id == item.letterhead_section_id,
        );
        if (index > -1) {
          //value exists
          transactionarray.push(
            this.prismaservice.doctor_letterhead_section.updateManyAndReturn({
              where: {
                letterhead_id: item.letterhead_id,
                letterhead_section_id: item.letterhead_section_id,
                isActive: true,
              },
              data: {
                value: item.value,
                updated_by: userEmail,
              },
            }),
          );
        } else {
          //value does not exist
          transactionarray.push(
            this.prismaservice.doctor_letterhead_section.createManyAndReturn({
              data: {
                letterhead_id: item.letterhead_id,
                letterhead_section_id: item.letterhead_section_id,
                value: item.value,
                created_by: userEmail,
              },
            }),
          );
        }
      });

      const res = await this.prismaservice.$transaction(transactionarray);
      return res;
    } catch (error) {
      this.logger.error(`Error upserting multiple doctor letterhead sections ${error}`, error?.stack);
      throw error;
    }
  }
}
