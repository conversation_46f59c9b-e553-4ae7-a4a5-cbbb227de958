import { Body, Controller, Post } from '@nestjs/common';
import { SubscriptionService } from './subscription.service';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { CreateSubscriptionDto } from './dto/subscription.dto';

@Controller('subscriptions')
export class SubscriptionController {
  constructor(private readonly subscriptionservice: SubscriptionService) {}

  @Post('admin')
  async createSubscription(
    @GetUserEmail() userEmail: string,
    @Body() model: CreateSubscriptionDto,
  ) {
    return await this.subscriptionservice.createSubsciption(userEmail, model);
  }
}
