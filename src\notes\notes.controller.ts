import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UsePipes,
  ValidationPipe,
} from '@nestjs/common';
import { Response } from 'express';
import { CreateNotesDTO, CreateNoteTypeDTO } from './dtos';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { NotesService } from './notes.service';
import { ApiBearerAuth, ApiOperation, ApiTags } from '@nestjs/swagger';
import { UpdateNotesDto } from './dtos/UpdateNotes.dto';
import { DeleteNotesDto } from './dtos/DeleteNotes.dto';
import { ApiResponseDTO, PaginationQueryDTO } from 'src/common/dtos';

@ApiTags('Notes')
@ApiBearerAuth('access-token')
@Controller('notes')
export class NotesController {
  constructor(private readonly notesService: NotesService) {}

  @Get('get-notes/encounter/:encounterId')
  async getNotesByEncounterId(@Param('encounterId') encounterId: string) {
    const result = await this.notesService.getNotesByEncounter(encounterId);
    return {
      data: result,
    };
  }

  @Post('create-note-type')
  @ApiOperation({ summary: 'Create a new note type for doctor' })
  async create(
    @Body() createNoteTypeDto: CreateNoteTypeDTO,
    @GetUserEmail() userEmail: string,
  ) {
    const response = await this.notesService.createNoteTypeWithSections(
      createNoteTypeDto,
      userEmail,
    );
    return {
      data: response,
    };
  }

  @ApiOperation({ summary: 'Generate note for encounter given a note type' })
  @Post('generate-note/encounter/:encounterId')
  @UsePipes(new ValidationPipe())
  async generateNotes(
    @Body() body: CreateNotesDTO,
    @Param('encounterId') encounterId: string,
    @GetUserEmail() userEmail: string,
  ) {
    const result = await this.notesService.generateNotesFromTranscription(
      encounterId,
      body.noteTypeId,
      userEmail,
      body.extraNoteTypes,
    );

    return {
      data: result,
    };
  }

  @Put('update-note/:noteDetailId')
  async updateNoteDetail(
    @Param('noteDetailId') note_detail_id: string,
    @Body() updateData: UpdateNotesDto,
    @GetUserEmail() userEmail: string,
  ) {
    const data = await this.notesService.updateNoteDetail(
      note_detail_id,
      updateData.note_section_id,
      { value: updateData.value },
      userEmail,
    );
    return {
      statusCode: 200,
      message: 'Notes content Updated Successfully',
      data,
    };
  }

  @Delete('delete-note/:noteDetailId')
  async deleteNoteDetail(
    @Param('noteDetailId') note_detail_id: string,
    @Body() deleteData: DeleteNotesDto,
    @GetUserEmail() userEmail: string,
  ) {
    const response = await this.notesService.deleteNoteDetail(
      note_detail_id,
      deleteData.note_section_id,
      userEmail,
    );
    return {
      statusCode: 204,
      message: 'Note Deleted Successfully',
      data: response,
    };
  }

  @Get('note-types')
  @ApiOperation({ summary: 'Get all note types for doctor' })
  async getNoteTypes(
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<any>> {
    const response =
      await this.notesService.getAllNoteTypesWithSections(userEmail);
    return {
      data: response,
    };
  }

  @Get('note-sections')
  @ApiOperation({ summary: 'Get all note sections for doctor' })
  async getNoteSections(
    @Query() pagination: PaginationQueryDTO,
    @GetUserEmail() userEmail: string,
  ) {
    const { page, limit } = pagination;
    return await this.notesService.getAllNoteSections(userEmail, page, limit);
  }
}
