import {
  Controller,
  Post,
  UseInterceptors,
  UploadedFile,
  Res,
  HttpStatus,
  <PERSON>gger,
  Param,
  BadRequestException,
  Query,
  HttpException,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { extname } from 'path';
import { Response } from 'express';
import { TranscribeService } from './transcribe.service';
import { s3FileUploadService } from '../common/s3-file-management/s3FileUpload.service';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import {
  ApiParam,
  ApiQuery,
  ApiTags,
  ApiConsumes,
  ApiOperation,
} from '@nestjs/swagger';
import { Public } from '../common/decorators/public-endpoint.decorator';
import { UtilsService } from '../common/utils/utils.service';

@ApiTags('Transcribe')
@Controller('transcribe')
export class TranscribeController {
  private readonly logger = new Logger(TranscribeController.name);

  constructor(
    private readonly transcribeService: TranscribeService,
    private readonly fileUploadService: s3FileUploadService,
    private readonly utilsService: UtilsService,
  ) {}

  @ApiQuery({
    name: 'duration',
    required: true,
    type: String,
    description: 'Duration of the audio file in seconds',
  })
  @ApiParam({
    name: 'encounterId',
    required: true,
    type: String,
    description: 'Unique identifier for the encounter',
  })
  @Post('upload/encounter/:encounterId')
  @UseInterceptors(
    FileInterceptor('audio', {
      fileFilter: (req, file, cb) => {
        const allowedTypes = [
          'audio/wav',
          'audio/wave',
          'audio/mpeg',
          'audio/webm',
          'video/webm',
          'audio/mp4',
          'audio/x-m4a',
          'video/mp4'
        ];
        if (allowedTypes.includes(file.mimetype)) {
          cb(null, true);
        } else {
          cb(
            new Error(
              `Only WAV files are supported for speech recognition file type = ${file.mimetype}`,
            ),
            false,
          );
        }
      },
      limits: {
        fileSize: 25 * 1024 * 1024, // Split on 25 MB audio
      },
    }),
  )
  async uploadAudio(
    @UploadedFile() file: Express.Multer.File,
    @Res() res: Response,
    @Param('encounterId') encounterId: string,
    @Query('duration') duration: string,
    @Query('type') type: string,
    @GetUserEmail() userEmail: string,
  ) {
    if (!duration || duration === '') {
      duration = '0';
    }
    if (!type || type === '') {
      this.logger.error('Transcription type is required but was not provided');
      throw new BadRequestException('Type is required');
    }
    if (!file) {
      throw new BadRequestException('No file uploaded');
    }

    if (!encounterId) {
      throw new BadRequestException('Encounter ID is required');
    }

    const appendTranscriptionCount =
      await this.transcribeService.getTranscriptionTypeCount(encounterId, type);

    if (appendTranscriptionCount >= 5) {
      this.logger.error(`Maximum number of transcriptions (5) reached for encounter ID: ${encounterId} with type: ${type}`);
      throw new BadRequestException(
        'You have reached the  maximum number of transcriptions for append',
      );
    }

    if (!file) {
      this.logger.warn(`No file uploaded or invalid file type for encounter ID: ${encounterId}`);
      return res
        .status(HttpStatus.BAD_REQUEST)
        .json({ message: 'Invalid file upload' });
    }

    let fileExtension = extname(file.originalname);
    if (!fileExtension) {
      fileExtension = file.mimetype.includes('webm') ? '.webm' : '.wav';
    }
    const uniqueFileName = `audio/${encounterId}/${new Date().toISOString()}${fileExtension}`;
    try {
      const s3Location = await this.fileUploadService.uploadFileToS3(
        file,
        uniqueFileName,
      );

      this.logger.log('Starting speech recognition');
      const transcription = await this.transcribeService.recognizeSpeech({
        ...file,
        s3Uri: s3Location,
        encounter_id: encounterId,
        user_email: userEmail,
        duration,
        type,
      });

      return res.status(HttpStatus.CREATED).json({
        message: 'Audio file uploaded and processed successfully',
        filename: uniqueFileName,
        s3Location,
        transcription,
      });
    } catch (error) {
      this.logger.error('Error uploading file:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to upload or process audio file',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}