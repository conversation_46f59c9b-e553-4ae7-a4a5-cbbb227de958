import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  ParseUUIDPipe,
  Patch,
  Post,
  Put,
} from '@nestjs/common';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
import { LabsService } from './labs.service';
import { Labs } from '@prisma/client';
import { ApiResponseDTO } from 'src/common/dtos';
import { CreateLabDto, LabDetailsDto, UpdateLabDto } from './dtos/labs.dto';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
@ApiTags('Labs')
@ApiBearerAuth('access-token')
@Controller('labs')
export class LabsController {
  constructor(private readonly labservice: LabsService) {}

  @Get('encounter/:encounterId')
  async getLabsByEncounterId(
    @Param('encounterId', ParseUUIDPipe) encounterId: string,
  ): Promise<ApiResponseDTO<Labs[]>> {
    return { data: await this.labservice.getLabsByEncounterId(encounterId) };
  }

  @Post()
  async createLab(
    @Body() data: CreateLabDto,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<Labs[]>> {
    return { data: await this.labservice.createLab(data, userEmail) };
  }

  @Put('update-lab/:lab_id')
  async updateLab(
    @Param('lab_id', ParseUUIDPipe) lab_id: string,
    @Body() data: UpdateLabDto,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<Labs>> {
    return { data: await this.labservice.updateLab(lab_id, data, userEmail) };
  }

  @Patch('delete-lab/:lab_id')
  async deleteLab(
    @Param('lab_id', ParseUUIDPipe) lab_id: string,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<Labs>> {
    return { data: await this.labservice.deleteLab(lab_id, userEmail) };
  }

  @Post('generate-labs/:encounter_id')
  async generateLabFromTranscription(
    @Param('encounter_id', ParseUUIDPipe) encounter_id: string,
    @GetUserEmail() userEmail: string,
  ): Promise<ApiResponseDTO<any>> {
    return {
      data: await this.labservice.generateLabFromTranscription(
        encounter_id,
        userEmail,
      ),
    };
  }

  @Patch(':lab_id/update-lab-status/:status')
  async updateLabStatusByLabId(
    @Param('lab_id', ParseUUIDPipe) lab_id: string,
    @Param('status') status: string,
    @GetUserEmail() userEmail: string,
    @Body() {status_reason}:{status_reason:string}
  ) {
    return await this.labservice.upadateLabStatusByLabid(
      lab_id,
      status,
      userEmail,
      status_reason
    );
  }
}
