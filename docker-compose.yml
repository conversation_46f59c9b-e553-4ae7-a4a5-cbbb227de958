version: '3.9'

services:
 
  app:
    build:
      context: .
      dockerfile: dockerfile
    env_file:
      - .env
    environment:
      - PORT=3002
      - FFMPEG_PATH=/usr/bin/ffmpeg
      - FFPROBE_PATH=/usr/bin/ffprobe
      - AWS_REGION=${AWS_REGION}
      - AWS_S3_BUCKET_NAME=${AWS_S3_BUCKET_NAME}
      - AUTH0_ISSUER_URL=${AUTH0_ISSUER_URL}
      - AUTH0_AUDIENCE=${AUTH0_AUDIENCE}
      - WHISPER_MODEL_PATH=${WHISPER_MODEL_PATH}
      - GOOGLE_CLIENT_EMAIL=${GOOGLE_CLIENT_EMAIL}
      - GOOGLE_PROJECT_ID=${GOOGLE_PROJECT_ID}
      - GOOGLE_CLIENT_ID=${GOOGLE_CLIENT_ID}
      - GOOGLE_BUCKET_NAME=${GOOGLE_BUCKET_NAME}
      - AWS_ACCESS_KEY_ID=${AWS_ACCESS_KEY_ID}
      - AWS_SECRET_ACCESS_KEY=${AWS_SECRET_ACCESS_KEY}
      - DATABASE_URL=${DATABASE_URL}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GOOGLE_PRIVATE_KEY=${GOOGLE_PRIVATE_KEY}
      - GOOGLE_PRIVATE_KEY_ID=${GOOGLE_PRIVATE_KEY_ID}

    container_name: ekko-coreservice-nodejs
    ports:
      - '3002:3002'
    volumes:
      - ./src:/app/src


