import {
  Injectable,
  Logger,
  HttpException,
  HttpStatus,
  NotFoundException,
  BadRequestException,
} from '@nestjs/common';
import { UtilsService } from 'src/common/utils/utils.service';

import { AudioTranscription } from '@prisma/client';
import { PrismaService } from 'src/common/prisma/prisma.service';

@Injectable()
export class TranscriptionsRepository {
  private readonly logger = new Logger(TranscriptionsRepository.name);
  constructor(
    private readonly prismaservice: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async getTranscriptionUsingEncounterId(
    encounter_id: string,
  ): Promise<AudioTranscription[]> {
    try {
      const transcription =
        await this.prismaservice.audioTranscription.findMany({
          where: {
            encounter_id,
            isActive: true,
            is_processed: true,
          },
        });
      if (!transcription || transcription.length == 0) {
        const statusExists = await this.prismaservice.encounterStatus.findFirst(
          {
            where: {
              status: 'NOT_STARTED',
              isActive: true,
            },
          },
        );

        if (!statusExists) {
          this.logger.error(`Invalid encounter status: NOT_STARTED not found in the system`);
          throw new BadRequestException('Invalid encounter status');
        }

        const encounterExistsCount = await this.prismaservice.encounter.count({
          where: {
            isActive: true,
            encounter_id: encounter_id,
          },
        });

        if (encounterExistsCount === 0) {
          this.logger.error(`Encounter not found for the given encounter ID: ${encounter_id}`);
          throw new HttpException(
            'encounter not found for update status',
            HttpStatus.NOT_FOUND,
          );
        }

        const encounter = await this.prismaservice.encounter.update({
          where: {
            encounter_id: encounter_id,
            isActive: true,
          },
          data: {
            encounter_status: 'NOT_STARTED',
          },
        });
        this.logger.error(`No transcriptions found for encounter ID: ${encounter_id}`);
        throw new NotFoundException('Transcription not found');
      }

      return transcription;
    } catch (error) {
      this.logger.error(`Error fetching transcription for encounter ID: ${encounter_id}`, error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to get transcription',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
