import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsAlpha,
  IsEmail,
  IsNotEmpty,
  IsOptional,
  IsPhoneNumber,
  MaxLength,
} from 'class-validator';

export class DoctorDetailsDTO {
  @ApiProperty({
    description: 'Unique identifier for the doctor',
    example: 'doctor-uuid-456',
  })
  doctorId: string;

  @ApiProperty({
    description: 'Email address of the doctor',
    example: '<EMAIL>',
  })
  email: string;
}
export class UpsertDoctorDto {
  @ApiProperty({
    description: 'Email address of the doctor',
    example: '<EMAIL>',
  })
  @IsEmail()
  @IsNotEmpty()
  email: string;

  @ApiProperty({
    description: 'First name of the doctor',
    example: 'Alice',
  })
  @IsNotEmpty({ message: ' first name is required' })
  @IsAlpha(undefined, {
    message: 'doctor first name must contain only letters',
  })
  first_name: string;

  @ApiProperty({
    description: 'Last name of the doctor',
    example: '<PERSON>',
  })
  @IsNotEmpty({ message: 'doctor last name is required' })
  @IsAlpha(undefined, {
    message: 'doctor last name must contain only letters',
  })
  last_name: string;

  @ApiPropertyOptional({
    description: "doctor's phone number in E.164 format",
    example: '+************',
  })
  @MaxLength(20, {
    message: 'doctor phone number must not exceed 20 characters',
  })
  phone: string;

  @ApiPropertyOptional({
    description: 'speciality of doctor',
    example: 'Cardiology',
  })
  @IsOptional()
  specialty?: string;

  @ApiPropertyOptional({
    description: 'country of practice',
    example: 'India',
  })
  @IsOptional()
  country_of_practice?: string;

  @ApiPropertyOptional({
    description: 'ehr of doctor',
    example: 'value of ehr',
  })
  @IsOptional()
  ehr?: string;
  @ApiPropertyOptional({
    description: 'tenant id of doctor',
    example: 'value of tenant id',
  })
  @IsOptional()
  tenant_id?: string;
}
