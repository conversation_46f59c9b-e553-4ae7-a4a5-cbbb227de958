import { Module } from '@nestjs/common';
import { EncounterModule } from './encounter/encounter.module';
import { TranscribeModule } from './transcribe/transcribe.module';
import { PrismaModule } from './common/prisma/prisma.module';
import { AuthModule } from './auth/auth.module';
import { DoctorModule } from './doctor/doctor.module';
import { HealthModule } from './common/health/health.module';
import { NotesModule } from './notes/notes.module';
import { PatientModule } from './patient/patient.module';
import { TranscriptionsModule } from './transcriptions/transcriptions.module';
import { LabsModule } from './labs/labs.module';
import { UtilsModule } from './common/utils/utils.module';
import { PrescriptionsModule } from './prescriptions/prescriptions.module';
import { PlanModule } from './plan/plan.module';
import { OnboardingModule } from './onboarding/onboarding.module';
import { SubscriptionModule } from './subscription/subscription.module';
import { LetterheadModule } from './letterhead/letterhead.module';
import { TenantModule } from './tenant/tenant.module';
import { FeedbackModule } from './feedback/feedback.module';
import { UniversalPicklistModule } from './universal-picklist/universal-picklist.module';

@Module({
  imports: [
    PrismaModule,
    EncounterModule,
    TranscribeModule,
    AuthModule,
    DoctorModule,
    HealthModule,
    NotesModule,
    PatientModule,
    TranscriptionsModule,
    LabsModule,
    UtilsModule,
    PrescriptionsModule,
    PlanModule,
    OnboardingModule,
    SubscriptionModule,
    LetterheadModule,
    TenantModule,
    FeedbackModule,
    UniversalPicklistModule,
  ],
  controllers: [],
  providers: [],
})
export class AppModule {}
