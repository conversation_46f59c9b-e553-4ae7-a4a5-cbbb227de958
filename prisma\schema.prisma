// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enum for Gender
enum Gender {
  MALE
  FEMALE
  OTHER
  UNKNOWN
  RATHER_NOT_SAY
}

// Doctor Model
model Doctor {
  doctor_id           String         @id @default(uuid()) @db.Uuid
  email               String         @unique
  first_name          String?
  last_name           String?
  phone               String?
  country_of_practice String?
  specialty           String?
  ehr                 String?
  isActive            Boolean        @default(true)
  isVerified          Boolean?       @default(false)
  // Relation 
  encounters          Encounter[]
  NoteHeader          NoteHeader[]
  Subscription        Subscription[]
  specialties         Specialties?   @relation(fields: [specialty], references: [name])

  // Auditing fields
  created_at       DateTime           @default(now())
  created_by       String             @default("ADMIN")
  updated_at       DateTime?          @updatedAt
  updated_by       String?
  DoctorLetterHead DoctorLetterHead[]
  TenantDoctor     TenantDoctor[]

  @@map("doctor")
}

// Patient Model
model Patient {
  patient_id    String   @id @default(uuid()) @db.Uuid
  first_name    String
  last_name     String
  date_of_birth DateTime @db.Timestamptz(3)
  gender        Gender
  isActive      Boolean  @default(true)

  phone_number       String @db.VarChar(20)
  phone_country_code String @db.VarChar(5)

  tenant_id String? @db.Uuid

  // Relations
  encounters           Encounter[]
  audio_transcriptions AudioTranscription[]
  NoteHeader           NoteHeader[]
  tenant               Tenant?              @relation(fields: [tenant_id], references: [Id])

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@index([phone_number], name: "idx_patient_phone_number")
  @@index([created_by], name: "idx_patient_created_by")
  @@map("patient")
}

// Encounter Model
model Encounter {
  encounter_id     String  @id @default(uuid()) @db.Uuid
  patient_id       String  @db.Uuid
  doctor_id        String  @db.Uuid
  note_type_id     String? @db.Uuid
  encounter_status String? @db.VarChar(50)
  isActive         Boolean @default(true)

  patient              Patient              @relation(fields: [patient_id], references: [patient_id])
  doctor               Doctor               @relation(fields: [doctor_id], references: [doctor_id])
  // Relations
  note_type            NoteType?            @relation(fields: [note_type_id], references: [note_type_id])
  encounterstatus      EncounterStatus?     @relation(fields: [encounter_status], references: [status])
  labs                 Labs[]
  // Relation to AudioTranscriptions
  audio_transcriptions AudioTranscription[]
  NoteHeader           NoteHeader[]

  encounter_date DateTime @db.Date

  // Auditing fields
  created_at        DateTime            @default(now())
  created_by        String              @default("ADMIN")
  updated_at        DateTime?           @updatedAt
  updated_by        String?
  Prescriptions     Prescriptions[]
  EncounterFeedback EncounterFeedback[]

  @@map("encounter")
}

// AudioTranscription Model
model AudioTranscription {
  id                             String                           @id @default(uuid()) @db.Uuid
  isActive                       Boolean                          @default(true)
  // File details
  original_filename              String                           @db.VarChar(255)
  s3_uri                         String                           @db.VarChar(512)
  file_size                      BigInt                           @db.BigInt
  mime_type                      String                           @db.VarChar(100)
  duration                       String?
  // Transcription details
  transcription                  String?                          @db.Text
  detected_language              String?                          @db.VarChar(10)
  raw_transcription              String?                          @db.Text
  // Relations
  patient_id                     String?                          @db.Uuid
  encounter_id                   String?                          @db.Uuid
  transcription_type             String?
  encounter                      Encounter?                       @relation(fields: [encounter_id], references: [encounter_id], onDelete: SetNull, onUpdate: Cascade)
  transcriptionType              TranscriptionType?               @relation(fields: [transcription_type], references: [transcription_type])
  // Auditing fields
  created_at                     DateTime                         @default(now())
  created_by                     String                           @default("SYSTEM") @db.VarChar(100)
  updated_at                     DateTime                         @updatedAt
  updated_by                     String                           @default("SYSTEM") @db.VarChar(100)
  // Optional status tracking
  is_processed                   Boolean                          @default(false)
  processing_error               String?                          @db.Text
  Patient                        Patient[]
  ResponseLLM                    ResponseLLM[]
  AudioTranscription_LLMResponse AudioTranscription_LLMResponse[]

  @@index([patient_id], name: "idx_audio_transcription_patient")
  @@index([encounter_id], name: "idx_audio_transcription_encounter")
  @@map("audio_transcription")
}

// NoteHeader Model
model NoteHeader {
  note_id      String  @id @default(uuid()) @db.Uuid
  patient_id   String  @db.Uuid
  encounter_id String  @db.Uuid
  doctor_id    String  @db.Uuid
  note_type_id String  @db.Uuid
  isActive     Boolean @default(true)

  patient   Patient   @relation(fields: [patient_id], references: [patient_id])
  encounter Encounter @relation(fields: [encounter_id], references: [encounter_id])
  doctor    Doctor    @relation(fields: [doctor_id], references: [doctor_id])
  note_type NoteType  @relation(fields: [note_type_id], references: [note_type_id])

  note_details NoteDetail[]

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@map("note_header")
}

// NoteDetail Model
model NoteDetail {
  note_detail_id  String  @id @default(uuid()) @db.Uuid
  note_header_id  String  @db.Uuid
  note_section_id String  @db.Uuid
  value           String?
  isActive        Boolean @default(true)

  note_header  NoteHeader  @relation(fields: [note_header_id], references: [note_id])
  note_section NoteSection @relation(fields: [note_section_id], references: [note_section_id])

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@map("note_detail")
}

// NoteType Model
model NoteType {
  note_type_id   String  @id @default(uuid()) @db.Uuid
  note_type_name String  @unique
  isActive       Boolean @default(true)

  note_headers       NoteHeader[]
  note_type_sections NoteTypeSection[]

  // Auditing fields
  created_at DateTime    @default(now())
  created_by String      @default("ADMIN")
  updated_at DateTime?   @updatedAt
  updated_by String?
  Encounter  Encounter[]

  @@map("note_type_lk")
}

// NoteSection Model
model NoteSection {
  note_section_id String  @id @default(uuid()) @db.Uuid
  section_name    String  @unique
  isActive        Boolean @default(true)

  note_details       NoteDetail[]
  note_type_sections NoteTypeSection[]

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@map("note_section_lk")
}

// NoteTypeSection Model
model NoteTypeSection {
  note_type_section_id String  @id @default(uuid()) @db.Uuid
  note_type_id         String  @db.Uuid
  note_section_id      String  @db.Uuid
  order                Int?
  isActive             Boolean @default(true)

  note_type    NoteType    @relation(fields: [note_type_id], references: [note_type_id])
  note_section NoteSection @relation(fields: [note_section_id], references: [note_section_id])

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@map("note_type_sections")
}

model EncounterStatus {
  status      String  @id
  label       String  @db.VarChar(50)
  description String?
  isActive    Boolean @default(true)

  // Auditing fields
  created_at DateTime    @default(now())
  created_by String      @default("ADMIN")
  updated_at DateTime?   @updatedAt
  updated_by String?
  Encounter  Encounter[]

  @@map("encounter_status")
}

model Labs {
  lab_id        String  @id @default(uuid()) @db.Uuid
  lab_name      String
  isActive      Boolean @default(true)
  notes         String?
  reason        String?
  encounter_id  String? @db.Uuid
  status        String?
  status_reason String? @db.Text

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  // Relations
  encounter Encounter? @relation(fields: [encounter_id], references: [encounter_id])
  Status    Status?    @relation(fields: [status], references: [status])

  @@index([encounter_id], name: "idx_labs_encounter")
  @@map("labs")
}

model Status {
  status      String  @id
  label       String  @db.VarChar(50)
  description String?
  isActive    Boolean @default(true)

  // Auditing fields
  created_at    DateTime        @default(now())
  created_by    String          @default("ADMIN")
  updated_at    DateTime?       @updatedAt
  updated_by    String?
  Labs          Labs[]
  Prescriptions Prescriptions[]

  @@map("status")
}

model ResponseType {
  type_name   String  @id
  label       String  @db.VarChar(50)
  description String?
  isActive    Boolean @default(true)

  // Auditing fields
  created_at  DateTime      @default(now())
  created_by  String        @default("ADMIN")
  updated_at  DateTime?     @updatedAt
  updated_by  String?
  ResponseLLM ResponseLLM[]

  @@map("response_type")
}

model ResponseLLM {
  responseId   String  @id @default(uuid()) @db.Uuid
  raw_response String  @db.Text
  successful   Boolean @default(false)
  isActive     Boolean @default(true)

  //relations
  transcriptionId String? @db.Uuid
  response_type   String?

  audioTranscription             AudioTranscription?              @relation(fields: [transcriptionId], references: [id])
  responseType                   ResponseType?                    @relation(fields: [response_type], references: [type_name])
  // Auditing fields
  created_at                     DateTime                         @default(now())
  created_by                     String                           @default("ADMIN")
  updated_at                     DateTime?                        @updatedAt
  updated_by                     String?
  AudioTranscription_LLMResponse AudioTranscription_LLMResponse[]

  @@map("response_llm")
}

model Prescriptions {
  prescription_id String  @id @default(uuid()) @db.Uuid
  drug_name       String?
  dose            String?
  sig             String?
  brand           String?
  patient_notes   String? @db.Text
  isActive        Boolean @default(true)
  encounter_id    String? @db.Uuid
  status          String?
  status_reason   String? @db.Text

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  // Relations
  encounter Encounter? @relation(fields: [encounter_id], references: [encounter_id])
  Status    Status?    @relation(fields: [status], references: [status])

  @@index([encounter_id], name: "idx_prescriptions_encounter")
  @@map("prescriptions")
}

model DoseUnit {
  dose_unit_id String  @id @default(uuid()) @db.Uuid
  name         String
  full_name    String?
  group        String?

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@map("dose_unit_lk")
}

model Plan {
  planId            String    @id @default(uuid()) @db.Uuid
  name              String
  description       String?
  isActive          Boolean   @default(true)
  effectiveDateFrom DateTime? @default(now())
  effectiveDateTo   DateTime?

  // Auditing fields
  created_at   DateTime?      @default(now())
  created_by   String         @default("ADMIN")
  updated_at   DateTime?      @updatedAt
  updated_by   String?
  Subscription Subscription[]

  @@map("plan")
}

model Subscription {
  subscriptionId    String    @id @default(uuid()) @db.Uuid
  planId            String?   @db.Uuid
  doctor_id         String?   @db.Uuid
  isActive          Boolean   @default(true)
  status            String?   @default("ACTIVE")
  effectiveDateFrom DateTime  @default(now())
  effectiveDateTo   DateTime?

  // Relations
  plan   Plan?   @relation(fields: [planId], references: [planId])
  doctor Doctor? @relation(fields: [doctor_id], references: [doctor_id])

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@map("subscription")
}

model Admin {
  adminId  String  @id @default(uuid()) @db.Uuid
  email    String?
  isActive Boolean @default(true)

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@map("admin")
}

model Specialties {
  name        String  @id
  label       String?
  description String?
  order       Int?
  isActive    Boolean @default(true)

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?
  Doctor     Doctor[]

  @@map("specialties")
}

model DoctorLetterHead {
  letterhead_id     String   @id @default(uuid()) @db.Uuid
  page_size         String?
  page_height       Float?
  page_width        Float?
  top_padding       Float?
  top_padding_px    Int?
  bottom_padding    Float?
  bottom_padding_px Int?
  units             String?
  type              String?
  doctor_id         String?  @db.Uuid
  is_default        Boolean?
  isActive          Boolean  @default(true)

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  //relations 
  doctor                    Doctor?                     @relation(fields: [doctor_id], references: [doctor_id])
  doctor_letterhead_section doctor_letterhead_section[]

  @@index([doctor_id], name: "idx_doctor_letterhead_doctor")
  @@map("doctor_letterhead")
}

model PageSize {
  page_size_id String  @id @default(uuid()) @db.Uuid
  page_size    String
  page_height  Float?
  page_width   Float?
  units        String?
  isActive     Boolean @default(true)

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@map("page_size")
}

model TranscriptionType {
  transcription_type String  @id
  type_description   String?
  isActive           Boolean @default(true)

  // Auditing fields
  created_at         DateTime             @default(now())
  created_by         String               @default("ADMIN")
  updated_at         DateTime?            @updatedAt
  updated_by         String?
  AudioTranscription AudioTranscription[]

  @@map("transcription_type")
}

model AudioTranscription_LLMResponse {
  id               String  @id @default(uuid()) @db.Uuid
  transcription_id String? @db.Uuid
  response_id      String? @db.Uuid
  isActive         Boolean @default(true)

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  //relations
  audio_transcription AudioTranscription? @relation(fields: [transcription_id], references: [id])
  responseLLM         ResponseLLM?        @relation(fields: [response_id], references: [responseId])

  @@map("audio_transcription_llm_response")
}

model letterhead_section {
  letterhead_section_id String  @id @default(uuid()) @db.Uuid
  label                 String
  type                  String
  size_constraint       Int?
  isActive              Boolean @default(true)

  // Auditing fields
  created_at                DateTime                    @default(now())
  created_by                String                      @default("ADMIN")
  updated_at                DateTime?                   @updatedAt
  updated_by                String?
  doctor_letterhead_section doctor_letterhead_section[]

  @@map("letterhead_section")
}

model doctor_letterhead_section {
  id                    String  @id @default(uuid()) @db.Uuid
  letterhead_id         String  @db.Uuid
  letterhead_section_id String  @db.Uuid
  value                 String?
  isActive              Boolean @default(true)

  //relations
  doctorletterhead   DoctorLetterHead?   @relation(fields: [letterhead_id], references: [letterhead_id])
  letterhead_section letterhead_section? @relation(fields: [letterhead_section_id], references: [letterhead_section_id])

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?
}

model Tenant {
  Id           String  @id @default(uuid()) @db.Uuid
  name         String? @unique
  phone_number String?
  email        String?
  isActive     Boolean @default(true)

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  //relations
  TenantDoctor TenantDoctor[]
  Patient      Patient[]

  @@map("tenant")
}

model TenantDoctor {
  id       String  @id @default(uuid()) @db.Uuid
  tenantId String? @db.Uuid
  doctorId String? @db.Uuid
  isActive Boolean @default(true)

  //relations
  tenant Tenant? @relation(fields: [tenantId], references: [Id])
  doctor Doctor? @relation(fields: [doctorId], references: [doctor_id])

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@map("tenant_doctor")
}

model EncounterFeedback {
  id           String  @id @default(uuid()) @db.Uuid
  encounter_id String? @db.Uuid
  feedback     String?
  isActive     Boolean @default(true)

  //relations
  encounter Encounter? @relation(fields: [encounter_id], references: [encounter_id])

  // Auditing fields
  created_at       DateTime           @default(now())
  created_by       String             @default("ADMIN")
  updated_at       DateTime?          @updatedAt
  updated_by       String?
  FeedbackEvidence FeedbackEvidence[]

  @@map("encounter_feedback")
}

model FeedbackEvidence {
  id             String  @id @default(uuid()) @db.Uuid
  feedbackId     String? @db.Uuid
  url            String?
  screenshot_url String?
  isActive       Boolean @default(true)

  //relations
  encounterfeedback EncounterFeedback? @relation(fields: [feedbackId], references: [id])

  // Auditing fields
  created_at DateTime  @default(now())
  created_by String    @default("ADMIN")
  updated_at DateTime? @updatedAt
  updated_by String?

  @@map("feedback_evidence")
}

model tbl_auto_code {
  id                  Int       @id @default(autoincrement())
  prefix              String?
  suffix              String?
  last_generated_code String?
  created_at          DateTime? @default(now()) @db.Timestamptz(3)
  created_by          String?
  updated_at          DateTime? @db.Timestamptz(3)
  updated_by          String?
  isActive            Boolean?  @default(true)
  last_increment      Int?
  digits              Int?

  @@map("tbl_auto_code")
}

model tbl_universal_picklist {
  Id                          String                        @id @default(uuid()) @db.Uuid
  desc_1                      String?
  desc_2                      String?
  isActive                    Boolean?                      @default(true)
  master_code                 String                        @unique @default(dbgenerated("generate_auto_code('UM'::text, ''::text, ''::text, ''::text)"))
  master_description          String?
  master_name                 String
  master_value_prefix         String
  created_by                  String?
  updated_by                  String?
  parent_code                 String?
  created_at                  DateTime?                     @default(now()) @db.Timestamptz(3)
  updated_at                  DateTime?                     @db.Timestamptz(3)
  tbl_universal_picklist_data tbl_universal_picklist_data[]

  @@map("tbl_universal_picklist")
}

model tbl_universal_picklist_data {
  Id                     String                 @id @default(uuid()) @db.Uuid
  desc_1                 String?
  desc_2                 String?
  isActive               Boolean?               @default(true)
  master_code            String
  master_code_prefix     String
  parent_code            String?
  record_description     String?
  record_id              String?
  record_name            String
  created_by             String?
  updated_by             String?
  parent_record_id       String?
  additional_info        Json?
  created_at             DateTime?              @default(now()) @db.Timestamptz(3)
  updated_at             DateTime?              @db.Timestamptz(3)
  picklist_id            String                 @db.Uuid
  tbl_universal_picklist tbl_universal_picklist @relation(fields: [picklist_id], references: [Id])

  @@map("tbl_universal_picklist_data")
}


model tbl_prompts {
  // Primary identifier
  prompt_id String @id @default(uuid()) @db.Uuid
  
  // Core prompt content
  prompt_user String
  prompt_system String?
  
  // Metadata and configuration
  name String?
  description String?
  api_name String?
  version String @default("1.0")
  
  // AI model parameters
  temperature Float? @default(0.7)
  max_tokens Int?
  top_p Float?
  frequency_penalty Float?
  presence_penalty Float?
  model_name String // e.g., "gpt-4", "claude-3"
  
  // Prompt variables and configuration
  prompt_variables String? // Store as JSON for flexibility
  default_values Json? // Default values for variables
  additional_info Json? // Any extra context or notes
  
  // Usage and categorization
  category String? // e.g., "code", "creative", "analysis"
  tags String? //  tags for better organization
  usage_count Int? @default(0)
  last_used DateTime?
  
  // State management
  isActive Boolean @default(true)
  is_template Boolean? @default(false) // Whether this is a reusable template
  
  // Auditing fields
  created_at DateTime @default(now()) @db.Timestamptz(3)
  created_by String @default("ADMIN")
  updated_at DateTime? @updatedAt @db.Timestamptz(3)
  updated_by String?

  @@map("tbl_prompts")
}