import { Injectable } from '@nestjs/common';
import { PatientRepository } from './patient.repository';
import { PatientDetailsDTO } from './dtos/PatientDetails.dto';
import { Patient } from '@prisma/client';
import { CreatePatientDetailsDto } from './dtos/CreatePatientDetails.dto';
import { PaginatedResultDTO } from 'src/common/dtos';

@Injectable()
export class PatientService {
  constructor(private readonly patientRepository: PatientRepository) {}

  async getPatientByPhone(
    phone: string,
    userEmail: string,
    page: number = 0,
    limit: number = 10,
    full_name: string | undefined = undefined,
  ): Promise<PaginatedResultDTO<Patient>> {
    return await this.patientRepository.getPatientByPhone(
      phone,
      userEmail,
      page,
      limit,
      full_name,
    );
  }

  async updatePatientDetails(
    model: CreatePatientDetailsDto,
    userEmail: string,
    patientId: string,
  ): Promise<Patient> {
    return await this.patientRepository.updatePatient(
      model,
      userEmail,
      patientId,
    );
  }
}
