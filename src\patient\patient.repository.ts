import {
  HttpException,
  Injectable,
  NotFoundException, Logger,
  HttpStatus,
} from '@nestjs/common';
import { CreatePatientDetailsDto } from './dtos/CreatePatientDetails.dto';
import { PatientDetailsDTO } from './dtos/PatientDetails.dto';
import { PrismaService } from 'src/common/prisma/prisma.service';
import { Gender, Patient } from '@prisma/client';
import { PaginatedResultDTO, PaginationResDTO } from 'src/common/dtos';
import { UtilsService } from 'src/common/utils/utils.service';

@Injectable()
export class PatientRepository {
  private readonly logger = new Logger(PatientRepository.name);

  constructor(
    private readonly prisma: PrismaService,
    private readonly utilsService: UtilsService,
  ) {}

  async insertPatient(
    data: CreatePatientDetailsDto,
    userEmail: string,
  ): Promise<Patient> {
    try {
      const patient = await this.prisma.patient.create({
        data: {
          first_name: data.patientFirstName,
          last_name: data.patientLastName,
          date_of_birth: new Date(data.patientDOB).toISOString(),
          gender: Gender[data.patientGender.toUpperCase()],
          phone_number: data.patientPhone || null,
          phone_country_code: data.phoneCountryCode || null,
          created_by: userEmail,
        },
      });

      return patient;
    } catch (error) {
      this.logger.error('Error creating patient:', error?.stack);
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to create patient record',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getPatientByPhone(
    phone: string,
    userEmail: string,
    page: number = 0,
    limit: number = 10,
    full_name: string | undefined = undefined,
  ): Promise<PaginatedResultDTO<Patient>> {
    try {
      let first_name: string = undefined;
      let last_name: string = undefined;
      if (full_name) {
        if (full_name.trim().includes(' ')) {
          first_name = full_name.split(' ')[0];
          last_name = full_name.split(' ')[1];
        }
      }

      const [patient, totalRecords] = await this.prisma.$transaction([
        this.prisma.patient.findMany({
          where: {
            isActive: true,
            AND: [
              {
                OR: [
                  // Phone number search
                  {
                    phone_number: phone
                      ? {
                          startsWith: phone,
                        }
                      : undefined,
                  },
                  // First name only
                  {
                    first_name:
                      !first_name && !last_name
                        ? {
                            startsWith: full_name,
                            mode: 'insensitive',
                          }
                        : undefined,
                  },
                  // Last name only
                  {
                    last_name:
                      !last_name && !first_name
                        ? {
                            startsWith: full_name,
                            mode: 'insensitive',
                          }
                        : undefined,
                  },
                  // Both first name AND last name
                  {
                    AND: [
                      {
                        first_name:
                          first_name && last_name
                            ? {
                                startsWith: first_name,
                                mode: 'insensitive',
                              }
                            : undefined,
                      },
                      {
                        last_name:
                          last_name && first_name
                            ? {
                                startsWith: last_name,
                                mode: 'insensitive',
                              }
                            : undefined,
                      },
                    ],
                  },
                ],
              },
              {
                OR: [
                  {
                    created_by: userEmail,
                  },
                  {
                    created_by: 'ADMIN',
                  },
                ],
              },
            ],
          },
          skip: (page - 1) * limit,
          take: limit,
        }),

        this.prisma.patient.count({
          where: {
            isActive: true,
            AND: [
              {
                OR: [
                  // Phone number search
                  {
                    phone_number: phone
                      ? {
                          startsWith: phone,
                        }
                      : undefined,
                  },
                  // First name only
                  {
                    first_name:
                      !first_name && !last_name
                        ? {
                            startsWith: full_name,
                            mode: 'insensitive',
                          }
                        : undefined,
                  },
                  // Last name only
                  {
                    last_name:
                      !last_name && !first_name
                        ? {
                            startsWith: full_name,
                            mode: 'insensitive',
                          }
                        : undefined,
                  },
                  // Both first name AND last name
                  {
                    AND: [
                      {
                        first_name:
                          first_name && last_name
                            ? {
                                startsWith: first_name,
                                mode: 'insensitive',
                              }
                            : undefined,
                      },
                      {
                        last_name:
                          last_name && first_name
                            ? {
                                startsWith: last_name,
                                mode: 'insensitive',
                              }
                            : undefined,
                      },
                    ],
                  },
                ],
              },
              {
                OR: [
                  {
                    created_by: userEmail,
                  },
                  {
                    created_by: 'ADMIN',
                  },
                ],
              },
            ],
          },
        }),
      ]);
      return {
        data: patient,
        pagination: this.utilsService.getPagination(page, limit, totalRecords),
      };
    } catch (error) {
      this.logger.error('Error retrieving patient:', error?.stack);
      throw  this.utilsService.formatErrorResponse(
        error,
        'Failed to retrieve patient',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
    
  }

  async updatePatient(
    model: CreatePatientDetailsDto,
    userEmail: string,
    patient_id: string,
  ): Promise<Patient> {
    try {
      const existingPatient = await this.prisma.patient.count({
        where: { patient_id: patient_id, isActive: true },
      });
      if (existingPatient === 0) {
        this.logger.error(`Patient not found for the given patient ID: ${patient_id}`);
        throw new NotFoundException('Patient not found');
      }

      const patient = await this.prisma.patient.update({
        where: { patient_id: patient_id, isActive: true },
        data: {
          first_name: model.patientFirstName,
          last_name: model.patientLastName,
          date_of_birth: new Date(model.patientDOB).toISOString(),
          gender: Gender[model.patientGender],
          phone_number: model.patientPhone,
          phone_country_code: model.phoneCountryCode || null,
          updated_by: userEmail,
          updated_at: new Date().toISOString(),
        },
      });
      return patient;
    } catch (error) {
      this.logger.error('Error updating Patient:', error?.stack);
      // If it's already a NotFoundException, keep the original status code
      if (error instanceof NotFoundException) {
        throw this.utilsService.formatErrorResponse(
          error,
          error.message,
          HttpStatus.NOT_FOUND,
        );
      }
      throw this.utilsService.formatErrorResponse(
        error,
        'Failed to update patient information',
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
