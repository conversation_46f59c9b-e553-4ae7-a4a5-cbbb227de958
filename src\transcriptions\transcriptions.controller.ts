import { <PERSON>, Get, Param, ParseUUIDPipe } from '@nestjs/common';
import { TranscriptionsService } from './transcriptions.service';
import { AudioTranscription } from '@prisma/client';
import { ApiResponseDTO } from 'src/common/dtos';
import { ApiBearerAuth, ApiTags } from '@nestjs/swagger';
@ApiTags('Transcriptions')
@ApiBearerAuth('access-token')
@Controller('transcriptions')
export class TranscriptionsController {
  constructor(private readonly transcriptionsService: TranscriptionsService) {}

  @Get('encounter/:encounter_id')
  async getTranscriptionUsingEncounterId(
    @Param('encounter_id', ParseUUIDPipe) encounter_id: string,
  ): Promise<ApiResponseDTO<AudioTranscription[]>> {
    return {
      data: await this.transcriptionsService.getTranscriptionUsingEncounterId(
        encounter_id,
      ),
    };
  }
}
