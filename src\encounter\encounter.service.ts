import { Injectable } from '@nestjs/common';
import { CreateEncounterDTO, EncounterDetailsDTO } from './dtos';
import { EncounterRepository } from './encounter.repository';
import { DoctorRepository } from '../doctor/doctor.repository';
import { PaginatedResultDTO } from '../common/dtos';
import { PatientRepository } from 'src/patient/patient.repository';
import { Encounter } from '@prisma/client';
import { EncounterSummeryDto } from './dtos/EncounterDetails.dto';

@Injectable()
export class EncounterService {
  constructor(
    private readonly encounterRepository: EncounterRepository,
    private readonly doctorRepository: DoctorRepository,
    private readonly patientRepository: PatientRepository,
  ) {}

  async insertEncounter(
    data: CreateEncounterDTO,
    userEmail: string,
  ): Promise<EncounterDetailsDTO> {
    const doctor =
      await this.doctorRepository.getOrCreateDoctorByEmail(userEmail);
    if (!data.patient_id) {
      const patient = await this.patientRepository.insertPatient(
        data,
        userEmail,
      );
      data.patient_id = patient.patient_id;
    }

    return await this.encounterRepository.insertEncounter(data, doctor);
  }

  async getEncounterById(encounterId: string): Promise<EncounterDetailsDTO> {
    return await this.encounterRepository.getEncounterById(encounterId);
  }

  async getAllEncountersByDoctorEmail(
    userEmail: string,
    page: number,
    limit: number,
    patient_name: string | undefined = undefined,
    patient_id: string | undefined = undefined,
  ): Promise<PaginatedResultDTO<EncounterDetailsDTO>> {
    return this.encounterRepository.getAllEncountersByDoctorEmail(
      userEmail,
      page,
      limit,
      patient_name,
      patient_id
    );
  }

  async updateEncounterStatus(
    encounterId: string,
    status: string,
  ): Promise<Encounter> {
    return this.encounterRepository.updateEncounterStatus(encounterId, status);
  }

  async signOffEncounter(encounterId: string, userEmail: string) {
    const isSuccess = await this.encounterRepository.singOffEncounter(
      encounterId,
      userEmail,
    );
    return { data: isSuccess, message: 'sign-off successful' };
  }

  async deleteEncounter(encounterId: string, userEmail: string) {
    return await this.encounterRepository.deleteEncounter(
      encounterId,
      userEmail,
    );
  }

  async getEncounterSummary(encounterId: string, model: EncounterSummeryDto) {
    return await this.encounterRepository.genEncounterSummary(
      encounterId,
      model,
    );
  }
}
