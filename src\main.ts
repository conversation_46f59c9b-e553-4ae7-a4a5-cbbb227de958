import { NestFactory, Reflector } from '@nestjs/core';
import { Logger, ValidationPipe } from '@nestjs/common';

import { AppModule } from './app.module';
import 'dotenv/config';
import { AllExceptionsFilter } from './common/filters/all-exceptions.filter';
import { JwtAuthGuard } from './auth/jwt-auth.guard';
import { setupSwagger } from './common/swagger/swagger.config';
import { ResponseInterceptor } from './common/interceptors/response.interceptor';

async function bootstrap() {
  const app = await NestFactory.create(AppModule, {
    cors: {
      origin: '*',
      methods: 'GET,HEAD,PUT,PATCH,POST,DELETE,OPTIONS',
      credentials: true,
    },
  });
  const logger = new Logger('Bootstrap');
  const port = process.env.PORT ?? 3000;
  console.log('PATH at runtime:', process.env.PATH);

  app.useGlobalPipes(
    new ValidationPipe({
      transform: true,
      transformOptions: {
        enableImplicitConversion: true,
      },
    }),
  );

  app.useGlobalInterceptors(new ResponseInterceptor());
  // Setup Global Exception Filters
  app.useGlobalFilters(new AllExceptionsFilter());

  // Setup Swagger
  setupSwagger(app);

  // Enable JWT authorization for all endpoint by default
  const reflector = app.get(Reflector);
  app.useGlobalGuards(new JwtAuthGuard(reflector));

  await app.listen(port);
  logger.log(`Swagger UI available at: http://localhost:${port}/api`);
}
bootstrap();
