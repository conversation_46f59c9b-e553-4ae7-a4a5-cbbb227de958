import {
  Body,
  Controller,
  Get,
  Param,
  ParseUUI<PERSON>ipe,
  Put,
  Query,
} from '@nestjs/common';
import { PatientService } from './patient.service';
import {
  ApiBearerAuth,
  ApiOperation,
  ApiQuery,
  ApiTags,
} from '@nestjs/swagger';
import { PatientDetailsDTO } from './dtos/PatientDetails.dto';
import { Patient } from '@prisma/client';
import {
  ApiResponseDTO,
  PaginatedResultDTO,
  PaginationQueryDTO,
} from 'src/common/dtos';
import { GetUserEmail } from 'src/common/decorators/get-user-email.decorator';
import { CreatePatientDetailsDto } from './dtos/CreatePatientDetails.dto';

@ApiTags('Patients')
@ApiBearerAuth('access-token')
@Controller('patients')
export class PatientController {
  constructor(private readonly patientservice: PatientService) {}

  @Get()
  @ApiOperation({ summary: 'Get patient details by phone number' })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Page number for pagination',
  })
  @ApiQuery({
    name: 'limit',
    required: false,
    type: Number,
    description: 'Number of items per page',
  })
  @ApiQuery({
    name: 'phone',
    required: false,
    type: Number,
    description: 'phone number for patient',
  })
  @ApiQuery({
    name: 'full_name',
    required: false,
    type: String,
    description: 'patient full name',
  })
  async getPatientByPhone(
    @Query() pagination: PaginationQueryDTO,
    @Query('phone') phone: string,
    @Query('full_name') full_name: string,
    @GetUserEmail() userEmail: string,
  ): Promise<PaginatedResultDTO<Patient>> {
    const { page, limit } = pagination;
  

    phone = !phone || phone == '' ? undefined : phone;

    return await this.patientservice.getPatientByPhone(
      phone,
      userEmail,
      page,
      limit,
     full_name
    );
  }

  @Put('/:patientId')
  @ApiOperation({ summary: 'Update patient details' })
  async updatePatientDetails(
    @Body() model: CreatePatientDetailsDto,
    @GetUserEmail() userEmail: string,
    @Param('patientId', ParseUUIDPipe) patientId: string,
  ) {
    return await this.patientservice.updatePatientDetails(
      model,
      userEmail,
      patientId,
    );
  }
}
